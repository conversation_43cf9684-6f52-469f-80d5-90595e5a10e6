<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
   "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<head>
    <title>Reference</title>
    <link rel="stylesheet" href="../ldoc.css" type="text/css" />
</head>
<body>

<div id="container">

<div id="product">
	<div id="product_logo"></div>
	<div id="product_name"><big><b></b></big></div>
	<div id="product_description"></div>
</div> <!-- id="product" -->


<div id="main">


<!-- Menu -->

<div id="navigation">
<br/>
<h1>Concord</h1>

<ul>
  <li><a href="../index.html">Index</a></li>
</ul>

<h2>Contents</h2>
<ul>
<li><a href="#Functions">Functions</a></li>
</ul>


<h2>Modules</h2>
<ul class="nowrap">
  <li><a href="../modules/Components.html">Components</a></li>
  <li><a href="../modules/Concord.html">Concord</a></li>
  <li><a href="../modules/type.html">type</a></li>
  <li><strong>utils</strong></li>
</ul>
<h2>Classes</h2>
<ul class="nowrap">
  <li><a href="../classes/Component.html">Component</a></li>
  <li><a href="../classes/Entity.html">Entity</a></li>
  <li><a href="../classes/List.html">List</a></li>
  <li><a href="../classes/Pool.html">Pool</a></li>
  <li><a href="../classes/System.html">System</a></li>
  <li><a href="../classes/World.html">World</a></li>
</ul>

</div>

<div id="content">

<h1>Module <code>utils</code></h1>
<p>Utils
 Helper module for misc operations</p>
<p></p>


<h2><a href="#Functions">Functions</a></h2>
<table class="function_list">
	<tr>
	<td class="name" nowrap><a href="#Utils.shallowCopy">Utils.shallowCopy (orig, target)</a></td>
	<td class="summary">Does a shallow copy of a table and appends it to a target table.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Utils.loadNamespace">Utils.loadNamespace (pathOrFiles, namespace)</a></td>
	<td class="summary">Requires files and puts them in a table.</td>
	</tr>
</table>

<br/>
<br/>


    <h2 class="section-header "><a name="Functions"></a>Functions</h2>

    <dl class="function">
    <dt>
    <a name = "Utils.shallowCopy"></a>
    <strong>Utils.shallowCopy (orig, target)</strong>
    </dt>
    <dd>
    Does a shallow copy of a table and appends it to a target table.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">orig</span>
         Table to copy
        </li>
        <li><span class="parameter">target</span>
         Table to append to
        </li>
    </ul>





</dd>
    <dt>
    <a name = "Utils.loadNamespace"></a>
    <strong>Utils.loadNamespace (pathOrFiles, namespace)</strong>
    </dt>
    <dd>
    Requires files and puts them in a table.
 Accepts a table of paths to Lua files: {"path/to/file_1", "path/to/another/file_2", "etc"}
 Accepts a path to a directory with Lua files: "my_files/here"


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">pathOrFiles</span>
         The table of paths or a path to a directory.
        </li>
        <li><span class="parameter">namespace</span>
         A table that will hold the required files
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.5">table</a></span>
        The namespace table
    </ol>




</dd>
</dl>


</div> <!-- id="content" -->
</div> <!-- id="main" -->
<div id="about">
<i>generated by <a href="http://github.com/stevedonovan/LDoc">LDoc 1.4.6</a></i>
<i style="float:right;">Last updated 2020-08-18 15:20:32 </i>
</div> <!-- id="about" -->
</div> <!-- id="container" -->
</body>
</html>
