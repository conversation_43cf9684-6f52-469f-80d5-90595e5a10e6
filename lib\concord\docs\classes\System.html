<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
   "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<head>
    <title>Reference</title>
    <link rel="stylesheet" href="../ldoc.css" type="text/css" />
</head>
<body>

<div id="container">

<div id="product">
	<div id="product_logo"></div>
	<div id="product_name"><big><b></b></big></div>
	<div id="product_description"></div>
</div> <!-- id="product" -->


<div id="main">


<!-- Menu -->

<div id="navigation">
<br/>
<h1>Concord</h1>

<ul>
  <li><a href="../index.html">Index</a></li>
</ul>

<h2>Contents</h2>
<ul>
<li><a href="#Methods">Methods</a></li>
<li><a href="#Callbacks">Callbacks </a></li>
</ul>


<h2>Classes</h2>
<ul class="nowrap">
  <li><a href="../classes/Component.html">Component</a></li>
  <li><a href="../classes/Entity.html">Entity</a></li>
  <li><a href="../classes/List.html">List</a></li>
  <li><a href="../classes/Pool.html">Pool</a></li>
  <li><strong>System</strong></li>
  <li><a href="../classes/World.html">World</a></li>
</ul>
<h2>Modules</h2>
<ul class="nowrap">
  <li><a href="../modules/Components.html">Components</a></li>
  <li><a href="../modules/Concord.html">Concord</a></li>
  <li><a href="../modules/type.html">type</a></li>
  <li><a href="../modules/utils.html">utils</a></li>
</ul>

</div>

<div id="content">

<h1>Class <code>System</code></h1>
<p>Iterates over Entities.</p>
<p> From these Entities its get Components and modify them.
 A System contains 1 or more Pools.
 A System is contained by 1 World.</p>


<h2><a href="#Methods">Methods</a></h2>
<table class="function_list">
	<tr>
	<td class="name" nowrap><a href="#System:new">System:new (table)</a></td>
	<td class="summary">Creates a new SystemClass.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#System:setEnabled">System:setEnabled (enable)</a></td>
	<td class="summary">Sets if the System is enabled</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#System:isEnabled">System:isEnabled ()</a></td>
	<td class="summary">Returns is the System is enabled</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#System:getWorld">System:getWorld ()</a></td>
	<td class="summary">Returns the World the System is in.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#System:hasName">System:hasName ()</a></td>
	<td class="summary">Returns true if the System has a name.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#System:getName">System:getName ()</a></td>
	<td class="summary">Returns the name of the System.</td>
	</tr>
</table>
<h2><a href="#Callbacks">Callbacks </a></h2>
<table class="function_list">
	<tr>
	<td class="name" nowrap><a href="#System:init">System:init (world)</a></td>
	<td class="summary">Callback for system initialization.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#System:onEnabled">System:onEnabled ()</a></td>
	<td class="summary">Callback for when a System is enabled.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#System:onDisabled">System:onDisabled ()</a></td>
	<td class="summary">Callback for when a System is disabled.</td>
	</tr>
</table>

<br/>
<br/>


    <h2 class="section-header "><a name="Methods"></a>Methods</h2>

    <dl class="function">
    <dt>
    <a name = "System:new"></a>
    <strong>System:new (table)</strong>
    </dt>
    <dd>
    Creates a new SystemClass.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">table</span>
         filters A table containing filters (name = {components...})
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/System.html#">System</a></span>
        A new SystemClass
    </ol>




</dd>
    <dt>
    <a name = "System:setEnabled"></a>
    <strong>System:setEnabled (enable)</strong>
    </dt>
    <dd>
    Sets if the System is enabled


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">enable</span>
            <span class="types"><span class="type">boolean</span></span>

        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/System.html#">System</a></span>
        self
    </ol>




</dd>
    <dt>
    <a name = "System:isEnabled"></a>
    <strong>System:isEnabled ()</strong>
    </dt>
    <dd>
    Returns is the System is enabled



    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "System:getWorld"></a>
    <strong>System:getWorld ()</strong>
    </dt>
    <dd>
    Returns the World the System is in.



    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/World.html#">World</a></span>

    </ol>




</dd>
    <dt>
    <a name = "System:hasName"></a>
    <strong>System:hasName ()</strong>
    </dt>
    <dd>
    Returns true if the System has a name.



    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "System:getName"></a>
    <strong>System:getName ()</strong>
    </dt>
    <dd>
    Returns the name of the System.



    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></span>

    </ol>




</dd>
</dl>
    <h2 class="section-header "><a name="Callbacks"></a>Callbacks </h2>

    <dl class="function">
    <dt>
    <a name = "System:init"></a>
    <strong>System:init (world)</strong>
    </dt>
    <dd>
    Callback for system initialization.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">world</span>
            <span class="types"><a class="type" href="../classes/World.html#">World</a></span>
         The World the System was added to
        </li>
    </ul>





</dd>
    <dt>
    <a name = "System:onEnabled"></a>
    <strong>System:onEnabled ()</strong>
    </dt>
    <dd>
    Callback for when a System is enabled.







</dd>
    <dt>
    <a name = "System:onDisabled"></a>
    <strong>System:onDisabled ()</strong>
    </dt>
    <dd>
    Callback for when a System is disabled.







</dd>
</dl>


</div> <!-- id="content" -->
</div> <!-- id="main" -->
<div id="about">
<i>generated by <a href="http://github.com/stevedonovan/LDoc">LDoc 1.4.6</a></i>
<i style="float:right;">Last updated 2020-08-18 15:20:32 </i>
</div> <!-- id="about" -->
</div> <!-- id="container" -->
</body>
</html>
