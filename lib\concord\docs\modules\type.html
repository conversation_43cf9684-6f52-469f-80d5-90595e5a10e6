<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
   "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<head>
    <title>Reference</title>
    <link rel="stylesheet" href="../ldoc.css" type="text/css" />
</head>
<body>

<div id="container">

<div id="product">
	<div id="product_logo"></div>
	<div id="product_name"><big><b></b></big></div>
	<div id="product_description"></div>
</div> <!-- id="product" -->


<div id="main">


<!-- Menu -->

<div id="navigation">
<br/>
<h1>Concord</h1>

<ul>
  <li><a href="../index.html">Index</a></li>
</ul>

<h2>Contents</h2>
<ul>
<li><a href="#Functions">Functions</a></li>
</ul>


<h2>Modules</h2>
<ul class="nowrap">
  <li><a href="../modules/Components.html">Components</a></li>
  <li><a href="../modules/Concord.html">Concord</a></li>
  <li><strong>type</strong></li>
  <li><a href="../modules/utils.html">utils</a></li>
</ul>
<h2>Classes</h2>
<ul class="nowrap">
  <li><a href="../classes/Component.html">Component</a></li>
  <li><a href="../classes/Entity.html">Entity</a></li>
  <li><a href="../classes/List.html">List</a></li>
  <li><a href="../classes/Pool.html">Pool</a></li>
  <li><a href="../classes/System.html">System</a></li>
  <li><a href="../classes/World.html">World</a></li>
</ul>

</div>

<div id="content">

<h1>Module <code>type</code></h1>
<p>Type
 Helper module to do easy type checking for Concord types</p>
<p></p>


<h2><a href="#Functions">Functions</a></h2>
<table class="function_list">
	<tr>
	<td class="name" nowrap><a href="#Type.isEntity">Type.isEntity (t)</a></td>
	<td class="summary">Returns if object is an Entity.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Type.isComponentClass">Type.isComponentClass (t)</a></td>
	<td class="summary">Returns if object is a ComponentClass.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Type.isComponent">Type.isComponent (t)</a></td>
	<td class="summary">Returns if object is a Component.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Type.isSystemClass">Type.isSystemClass (t)</a></td>
	<td class="summary">Returns if object is a SystemClass.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Type.isSystem">Type.isSystem (t)</a></td>
	<td class="summary">Returns if object is a System.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Type.isWorld">Type.isWorld (t)</a></td>
	<td class="summary">Returns if object is a World.</td>
	</tr>
</table>

<br/>
<br/>


    <h2 class="section-header "><a name="Functions"></a>Functions</h2>

    <dl class="function">
    <dt>
    <a name = "Type.isEntity"></a>
    <strong>Type.isEntity (t)</strong>
    </dt>
    <dd>
    Returns if object is an Entity.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">t</span>
         Object to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "Type.isComponentClass"></a>
    <strong>Type.isComponentClass (t)</strong>
    </dt>
    <dd>
    Returns if object is a ComponentClass.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">t</span>
         Object to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "Type.isComponent"></a>
    <strong>Type.isComponent (t)</strong>
    </dt>
    <dd>
    Returns if object is a Component.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">t</span>
         Object to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "Type.isSystemClass"></a>
    <strong>Type.isSystemClass (t)</strong>
    </dt>
    <dd>
    Returns if object is a SystemClass.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">t</span>
         Object to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "Type.isSystem"></a>
    <strong>Type.isSystem (t)</strong>
    </dt>
    <dd>
    Returns if object is a System.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">t</span>
         Object to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "Type.isWorld"></a>
    <strong>Type.isWorld (t)</strong>
    </dt>
    <dd>
    Returns if object is a World.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">t</span>
         Object to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
</dl>


</div> <!-- id="content" -->
</div> <!-- id="main" -->
<div id="about">
<i>generated by <a href="http://github.com/stevedonovan/LDoc">LDoc 1.4.6</a></i>
<i style="float:right;">Last updated 2020-08-18 15:20:32 </i>
</div> <!-- id="about" -->
</div> <!-- id="container" -->
</body>
</html>
