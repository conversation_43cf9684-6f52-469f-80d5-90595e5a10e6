<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
   "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<head>
    <title>Reference</title>
    <link rel="stylesheet" href="../ldoc.css" type="text/css" />
</head>
<body>

<div id="container">

<div id="product">
	<div id="product_logo"></div>
	<div id="product_name"><big><b></b></big></div>
	<div id="product_description"></div>
</div> <!-- id="product" -->


<div id="main">


<!-- Menu -->

<div id="navigation">
<br/>
<h1>Concord</h1>

<ul>
  <li><a href="../index.html">Index</a></li>
</ul>

<h2>Contents</h2>
<ul>
<li><a href="#Methods">Methods</a></li>
</ul>


<h2>Classes</h2>
<ul class="nowrap">
  <li><strong>Assemblage</strong></li>
  <li><a href="../classes/Component.html">Component</a></li>
  <li><a href="../classes/Entity.html">Entity</a></li>
  <li><a href="../classes/List.html">List</a></li>
  <li><a href="../classes/Pool.html">Pool</a></li>
  <li><a href="../classes/System.html">System</a></li>
  <li><a href="../classes/World.html">World</a></li>
</ul>
<h2>Modules</h2>
<ul class="nowrap">
  <li><a href="../modules/Assemblages.html">Assemblages</a></li>
  <li><a href="../modules/Components.html">Components</a></li>
  <li><a href="../modules/Concord.html">Concord</a></li>
  <li><a href="../modules/Systems.html">Systems</a></li>
  <li><a href="../modules/type.html">type</a></li>
  <li><a href="../modules/utils.html">utils</a></li>
  <li><a href="../modules/worlds.html">worlds</a></li>
</ul>

</div>

<div id="content">

<h1>Class <code>Assemblage</code></h1>
<p>Gives an entity a set of components.</p>
<p></p>


<h2><a href="#Methods">Methods</a></h2>
<table class="function_list">
	<tr>
	<td class="name" nowrap><a href="#Assemblage:new">Assemblage:new (assemble)</a></td>
	<td class="summary">Creates a new Assemblage.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Assemblage:assemble">Assemblage:assemble (e, ...)</a></td>
	<td class="summary">Assembles an Entity.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Assemblage:hasName">Assemblage:hasName ()</a></td>
	<td class="summary">Returns true if the Assemblage has a name.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Assemblage:getName">Assemblage:getName ()</a></td>
	<td class="summary">Returns the name of the Assemblage.</td>
	</tr>
</table>

<br/>
<br/>


    <h2 class="section-header "><a name="Methods"></a>Methods</h2>

    <dl class="function">
    <dt>
    <a name = "Assemblage:new"></a>
    <strong>Assemblage:new (assemble)</strong>
    </dt>
    <dd>
    Creates a new Assemblage.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">assemble</span>
            <span class="types"><span class="type">function</span></span>
         Function that assembles an Entity
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/Assemblage.html#">Assemblage</a></span>
        A new assemblage
    </ol>




</dd>
    <dt>
    <a name = "Assemblage:assemble"></a>
    <strong>Assemblage:assemble (e, ...)</strong>
    </dt>
    <dd>
    Assembles an Entity.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">e</span>
            <span class="types"><a class="type" href="../classes/Entity.html#">Entity</a></span>
         Entity to assemble
        </li>
        <li><span class="parameter">...</span>
         additional arguments to pass to the assemble function
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/Assemblage.html#">Assemblage</a></span>
        self
    </ol>




</dd>
    <dt>
    <a name = "Assemblage:hasName"></a>
    <strong>Assemblage:hasName ()</strong>
    </dt>
    <dd>
    Returns true if the Assemblage has a name.



    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "Assemblage:getName"></a>
    <strong>Assemblage:getName ()</strong>
    </dt>
    <dd>
    Returns the name of the Assemblage.



    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></span>

    </ol>




</dd>
</dl>


</div> <!-- id="content" -->
</div> <!-- id="main" -->
<div id="about">
<i>generated by <a href="http://github.com/stevedonovan/LDoc">LDoc 1.4.6</a></i>
<i style="float:right;">Last updated 2020-01-04 10:27:07 </i>
</div> <!-- id="about" -->
</div> <!-- id="container" -->
</body>
</html>
