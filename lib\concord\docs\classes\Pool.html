<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
   "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<head>
    <title>Reference</title>
    <link rel="stylesheet" href="../ldoc.css" type="text/css" />
</head>
<body>

<div id="container">

<div id="product">
	<div id="product_logo"></div>
	<div id="product_name"><big><b></b></big></div>
	<div id="product_description"></div>
</div> <!-- id="product" -->


<div id="main">


<!-- Menu -->

<div id="navigation">
<br/>
<h1>Concord</h1>

<ul>
  <li><a href="../index.html">Index</a></li>
</ul>

<h2>Contents</h2>
<ul>
<li><a href="#Methods">Methods</a></li>
</ul>


<h2>Classes</h2>
<ul class="nowrap">
  <li><a href="../classes/Component.html">Component</a></li>
  <li><a href="../classes/Entity.html">Entity</a></li>
  <li><a href="../classes/List.html">List</a></li>
  <li><strong>Pool</strong></li>
  <li><a href="../classes/System.html">System</a></li>
  <li><a href="../classes/World.html">World</a></li>
</ul>
<h2>Modules</h2>
<ul class="nowrap">
  <li><a href="../modules/Components.html">Components</a></li>
  <li><a href="../modules/Concord.html">Concord</a></li>
  <li><a href="../modules/type.html">type</a></li>
  <li><a href="../modules/utils.html">utils</a></li>
</ul>

</div>

<div id="content">

<h1>Class <code>Pool</code></h1>
<p>Used to iterate over Entities with a specific Components
 A Pool contain a any amount of Entities.</p>
<p></p>


<h2><a href="#Methods">Methods</a></h2>
<table class="function_list">
	<tr>
	<td class="name" nowrap><a href="#Pool:new">Pool:new (name, filter)</a></td>
	<td class="summary">Creates a new Pool</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Pool:eligible">Pool:eligible (e)</a></td>
	<td class="summary">Checks if an Entity is eligible for the Pool.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Pool:evaluate">Pool:evaluate (e)</a></td>
	<td class="summary">Evaluate whether an Entity should be added or removed from the Pool.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Pool:getName">Pool:getName ()</a></td>
	<td class="summary">Gets the name of the Pool</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Pool:getFilter">Pool:getFilter ()</a></td>
	<td class="summary">Gets the filter of the Pool.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Pool:onEntityAdded">Pool:onEntityAdded (e)</a></td>
	<td class="summary">Callback for when an Entity is added to the Pool.</td>
	</tr>
</table>

<br/>
<br/>


    <h2 class="section-header "><a name="Methods"></a>Methods</h2>

    <dl class="function">
    <dt>
    <a name = "Pool:new"></a>
    <strong>Pool:new (name, filter)</strong>
    </dt>
    <dd>
    Creates a new Pool


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">name</span>
            <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></span>
         Name for the Pool.
        </li>
        <li><span class="parameter">filter</span>
            <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.5">table</a></span>
         Table containing the required BaseComponents
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/Pool.html#">Pool</a></span>
        The new Pool
    </ol>




</dd>
    <dt>
    <a name = "Pool:eligible"></a>
    <strong>Pool:eligible (e)</strong>
    </dt>
    <dd>
    Checks if an Entity is eligible for the Pool.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">e</span>
            <span class="types"><a class="type" href="../classes/Entity.html#">Entity</a></span>
         Entity to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "Pool:evaluate"></a>
    <strong>Pool:evaluate (e)</strong>
    </dt>
    <dd>
    Evaluate whether an Entity should be added or removed from the Pool.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">e</span>
         Entity to add or remove
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/Pool.html#">Pool</a></span>
        self
    </ol>




</dd>
    <dt>
    <a name = "Pool:getName"></a>
    <strong>Pool:getName ()</strong>
    </dt>
    <dd>
    Gets the name of the Pool



    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></span>

    </ol>




</dd>
    <dt>
    <a name = "Pool:getFilter"></a>
    <strong>Pool:getFilter ()</strong>
    </dt>
    <dd>
    Gets the filter of the Pool.
 Warning: Do not modify this filter.



    <h3>Returns:</h3>
    <ol>

        Filter of the Pool.
    </ol>




</dd>
    <dt>
    <a name = "Pool:onEntityAdded"></a>
    <strong>Pool:onEntityAdded (e)</strong>
    </dt>
    <dd>
    Callback for when an Entity is added to the Pool.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">e</span>
            <span class="types"><a class="type" href="../classes/Entity.html#">Entity</a></span>
         Entity that was added.
        </li>
    </ul>





</dd>
</dl>


</div> <!-- id="content" -->
</div> <!-- id="main" -->
<div id="about">
<i>generated by <a href="http://github.com/stevedonovan/LDoc">LDoc 1.4.6</a></i>
<i style="float:right;">Last updated 2020-08-18 15:20:32 </i>
</div> <!-- id="about" -->
</div> <!-- id="container" -->
</body>
</html>
