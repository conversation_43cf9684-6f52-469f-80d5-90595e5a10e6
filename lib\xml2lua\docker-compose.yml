version: '2.1'

services:
  test:
    command: bash -c "luarocks make && busted --shuffle"
    build:
      context: .
      dockerfile: test.Dockerfile
    volumes:
      - ".:/lua/"
    working_dir: "/lua"

  lint:
    command: luacheck -q .
    build:
      context: .
      dockerfile: test.Dockerfile
    volumes:
      - ".:/lua/"
    working_dir: "/lua"

  upload:
    environment:
      - LUA_ROCKS_API_KEY
    #the command doesn't work without the bash -c, even being a single command
    command: bash -c "luarocks upload xml2lua-*.rockspec --force --api-key=$LUA_ROCKS_API_KEY"
    build:
      context: .
      dockerfile: test.Dockerfile
    volumes:
      - ".:/lua/"
    working_dir: "/lua"
    depends_on:
      - test
