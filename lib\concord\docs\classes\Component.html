<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
   "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<head>
    <title>Reference</title>
    <link rel="stylesheet" href="../ldoc.css" type="text/css" />
</head>
<body>

<div id="container">

<div id="product">
	<div id="product_logo"></div>
	<div id="product_name"><big><b></b></big></div>
	<div id="product_description"></div>
</div> <!-- id="product" -->


<div id="main">


<!-- Menu -->

<div id="navigation">
<br/>
<h1>Concord</h1>

<ul>
  <li><a href="../index.html">Index</a></li>
</ul>

<h2>Contents</h2>
<ul>
<li><a href="#Methods">Methods</a></li>
</ul>


<h2>Classes</h2>
<ul class="nowrap">
  <li><strong>Component</strong></li>
  <li><a href="../classes/Entity.html">Entity</a></li>
  <li><a href="../classes/List.html">List</a></li>
  <li><a href="../classes/Pool.html">Pool</a></li>
  <li><a href="../classes/System.html">System</a></li>
  <li><a href="../classes/World.html">World</a></li>
</ul>
<h2>Modules</h2>
<ul class="nowrap">
  <li><a href="../modules/Components.html">Components</a></li>
  <li><a href="../modules/Concord.html">Concord</a></li>
  <li><a href="../modules/type.html">type</a></li>
  <li><a href="../modules/utils.html">utils</a></li>
</ul>

</div>

<div id="content">

<h1>Class <code>Component</code></h1>
<p>A pure data container that is contained by a single entity.</p>
<p></p>


<h2><a href="#Methods">Methods</a></h2>
<table class="function_list">
	<tr>
	<td class="name" nowrap><a href="#Component:new">Component:new (populate)</a></td>
	<td class="summary">Creates a new ComponentClass.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Component:hasName">Component:hasName ()</a></td>
	<td class="summary">Returns true if the Component has a name.</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#Component:getName">Component:getName ()</a></td>
	<td class="summary">Returns the name of the Component.</td>
	</tr>
</table>

<br/>
<br/>


    <h2 class="section-header "><a name="Methods"></a>Methods</h2>

    <dl class="function">
    <dt>
    <a name = "Component:new"></a>
    <strong>Component:new (populate)</strong>
    </dt>
    <dd>
    Creates a new ComponentClass.


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">populate</span>
            <span class="types"><span class="type">function</span></span>
         Function that populates a Component with values
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/Component.html#">Component</a></span>
        A new ComponentClass
    </ol>




</dd>
    <dt>
    <a name = "Component:hasName"></a>
    <strong>Component:hasName ()</strong>
    </dt>
    <dd>
    Returns true if the Component has a name.



    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "Component:getName"></a>
    <strong>Component:getName ()</strong>
    </dt>
    <dd>
    Returns the name of the Component.



    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></span>

    </ol>




</dd>
</dl>


</div> <!-- id="content" -->
</div> <!-- id="main" -->
<div id="about">
<i>generated by <a href="http://github.com/stevedonovan/LDoc">LDoc 1.4.6</a></i>
<i style="float:right;">Last updated 2020-08-18 15:20:32 </i>
</div> <!-- id="about" -->
</div> <!-- id="container" -->
</body>
</html>
