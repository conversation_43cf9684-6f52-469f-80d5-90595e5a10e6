<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
   "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<head>
    <title>Reference</title>
    <link rel="stylesheet" href="../ldoc.css" type="text/css" />
</head>
<body>

<div id="container">

<div id="product">
	<div id="product_logo"></div>
	<div id="product_name"><big><b></b></big></div>
	<div id="product_description"></div>
</div> <!-- id="product" -->


<div id="main">


<!-- Menu -->

<div id="navigation">
<br/>
<h1>Concord</h1>

<ul>
  <li><a href="../index.html">Index</a></li>
</ul>

<h2>Contents</h2>
<ul>
<li><a href="#Functions">Functions</a></li>
</ul>


<h2>Modules</h2>
<ul class="nowrap">
  <li><strong>Components</strong></li>
  <li><a href="../modules/Concord.html">Concord</a></li>
  <li><a href="../modules/type.html">type</a></li>
  <li><a href="../modules/utils.html">utils</a></li>
</ul>
<h2>Classes</h2>
<ul class="nowrap">
  <li><a href="../classes/Component.html">Component</a></li>
  <li><a href="../classes/Entity.html">Entity</a></li>
  <li><a href="../classes/List.html">List</a></li>
  <li><a href="../classes/Pool.html">Pool</a></li>
  <li><a href="../classes/System.html">System</a></li>
  <li><a href="../classes/World.html">World</a></li>
</ul>

</div>

<div id="content">

<h1>Module <code>Components</code></h1>
<p>Container for registered ComponentClasses</p>
<p></p>


<h2><a href="#Functions">Functions</a></h2>
<table class="function_list">
	<tr>
	<td class="name" nowrap><a href="#has">has (name)</a></td>
	<td class="summary">Returns true if the containter has the ComponentClass with the specified name</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#try">try (name)</a></td>
	<td class="summary">Returns true and the ComponentClass if one was registered with the specified name
 or false and an error otherwise</td>
	</tr>
	<tr>
	<td class="name" nowrap><a href="#get">get (name)</a></td>
	<td class="summary">Returns the ComponentClass with the specified name</td>
	</tr>
</table>

<br/>
<br/>


    <h2 class="section-header "><a name="Functions"></a>Functions</h2>

    <dl class="function">
    <dt>
    <a name = "has"></a>
    <strong>has (name)</strong>
    </dt>
    <dd>
    Returns true if the containter has the ComponentClass with the specified name


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">name</span>
            <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></span>
         Name of the ComponentClass to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><span class="type">boolean</span></span>

    </ol>




</dd>
    <dt>
    <a name = "try"></a>
    <strong>try (name)</strong>
    </dt>
    <dd>
    Returns true and the ComponentClass if one was registered with the specified name
 or false and an error otherwise


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">name</span>
            <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></span>
         Name of the ComponentClass to check
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>
        <li>
           <span class="types"><span class="type">boolean</span></span>
        </li>
        <li>
           <span class="types"><a class="type" href="../classes/Component.html#">Component</a></span>
        or error string</li>
    </ol>




</dd>
    <dt>
    <a name = "get"></a>
    <strong>get (name)</strong>
    </dt>
    <dd>
    Returns the ComponentClass with the specified name


    <h3>Parameters:</h3>
    <ul>
        <li><span class="parameter">name</span>
            <span class="types"><a class="type" href="https://www.lua.org/manual/5.1/manual.html#5.4">string</a></span>
         Name of the ComponentClass to get
        </li>
    </ul>

    <h3>Returns:</h3>
    <ol>

           <span class="types"><a class="type" href="../classes/Component.html#">Component</a></span>

    </ol>




</dd>
</dl>


</div> <!-- id="content" -->
</div> <!-- id="main" -->
<div id="about">
<i>generated by <a href="http://github.com/stevedonovan/LDoc">LDoc 1.4.6</a></i>
<i style="float:right;">Last updated 2020-08-18 15:20:32 </i>
</div> <!-- id="about" -->
</div> <!-- id="container" -->
</body>
</html>
