<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
   "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
<head>
    <title>Reference</title>
    <link rel="stylesheet" href="ldoc.css" type="text/css" />
</head>
<body>

<div id="container">

<div id="product">
	<div id="product_logo"></div>
	<div id="product_name"><big><b></b></big></div>
	<div id="product_description"></div>
</div> <!-- id="product" -->


<div id="main">


<!-- Menu -->

<div id="navigation">
<br/>
<h1>Concord</h1>




<h2>Modules</h2>
<ul class="nowrap">
  <li><a href="modules/Components.html">Components</a></li>
  <li><a href="modules/Concord.html">Concord</a></li>
  <li><a href="modules/type.html">type</a></li>
  <li><a href="modules/utils.html">utils</a></li>
</ul>
<h2>Classes</h2>
<ul class="nowrap">
  <li><a href="classes/Component.html">Component</a></li>
  <li><a href="classes/Entity.html">Entity</a></li>
  <li><a href="classes/List.html">List</a></li>
  <li><a href="classes/Pool.html">Pool</a></li>
  <li><a href="classes/System.html">System</a></li>
  <li><a href="classes/World.html">World</a></li>
</ul>

</div>

<div id="content">


  <h2>A feature-complete ECS library</h2>

<h2>Modules</h2>
<table class="module_list">
	<tr>
		<td class="name"  nowrap><a href="modules/Components.html">Components</a></td>
		<td class="summary">Container for registered ComponentClasses</td>
	</tr>
	<tr>
		<td class="name"  nowrap><a href="modules/Concord.html">Concord</a></td>
		<td class="summary"></td>
	</tr>
	<tr>
		<td class="name"  nowrap><a href="modules/type.html">type</a></td>
		<td class="summary">Type
 Helper module to do easy type checking for Concord types</td>
	</tr>
	<tr>
		<td class="name"  nowrap><a href="modules/utils.html">utils</a></td>
		<td class="summary">Utils
 Helper module for misc operations</td>
	</tr>
</table>
<h2>Classes</h2>
<table class="module_list">
	<tr>
		<td class="name"  nowrap><a href="classes/Component.html">Component</a></td>
		<td class="summary">A pure data container that is contained by a single entity.</td>
	</tr>
	<tr>
		<td class="name"  nowrap><a href="classes/Entity.html">Entity</a></td>
		<td class="summary">An object that exists in a world.</td>
	</tr>
	<tr>
		<td class="name"  nowrap><a href="classes/List.html">List</a></td>
		<td class="summary">Data structure that allows for fast removal at the cost of containing order.</td>
	</tr>
	<tr>
		<td class="name"  nowrap><a href="classes/Pool.html">Pool</a></td>
		<td class="summary">Used to iterate over Entities with a specific Components
 A Pool contain a any amount of Entities.</td>
	</tr>
	<tr>
		<td class="name"  nowrap><a href="classes/System.html">System</a></td>
		<td class="summary">Iterates over Entities.</td>
	</tr>
	<tr>
		<td class="name"  nowrap><a href="classes/World.html">World</a></td>
		<td class="summary">A collection of Systems and Entities.</td>
	</tr>
</table>

</div> <!-- id="content" -->
</div> <!-- id="main" -->
<div id="about">
<i>generated by <a href="http://github.com/stevedonovan/LDoc">LDoc 1.4.6</a></i>
<i style="float:right;">Last updated 2020-08-18 15:20:32 </i>
</div> <!-- id="about" -->
</div> <!-- id="container" -->
</body>
</html>
